const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
const logger = require('./server/utils/logger');

// Importar rutas
const authRoutes = require('./server/routes/auth');
const apiRoutes = require('./server/routes/api');

// Configuración de variables de entorno
dotenv.config();

const app = express();
const port = process.env.PORT || 3000;
const isDevelopment = process.env.NODE_ENV !== 'production';

// Configuración de CORS
const corsOptions = {
  origin: isDevelopment
    ? ['http://localhost:3001']  // Solo permitir el puerto de desarrollo de React
    : process.env.CORS_ORIGINS?.split(','),
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['Content-Range', 'X-Content-Range'],
  credentials: true,
  maxAge: 600
};

// Middleware simplificado para evitar error 431
app.use(cors({
  origin: ['http://localhost:3001', 'http://localhost:3000'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: false // Desactivar credentials para evitar cookies problemáticas
}));

// Middleware mínimo para parsing
app.use(express.json({ limit: '1mb' }));
app.use(express.urlencoded({ extended: true, limit: '1mb' }));

// Middleware de diagnóstico para headers
app.use((req, res, next) => {
  const headerSize = JSON.stringify(req.headers).length;
  console.log(`Request to ${req.path} - Header size: ${headerSize} bytes`);

  if (headerSize > 8192) { // 8KB limit
    console.warn('Large headers detected:', Object.keys(req.headers));
  }

  next();
});

// Endpoint de login simplificado para diagnóstico
app.post('/api/auth/simple-login', async (req, res) => {
  try {
    const { email, password } = req.body;
    console.log('Simple login attempt:', { email });

    // Validación básica
    if (email === '<EMAIL>' && password === 'admin123') {
      res.json({
        success: true,
        token: 'simple-token-123',
        user: { id: 'sandbox-user-01', email, role: 'admin' }
      });
    } else if (email === '<EMAIL>' && password === 'user123') {
      res.json({
        success: true,
        token: 'simple-token-456',
        user: { id: 'user-02', email, role: 'user' }
      });
    } else {
      res.status(401).json({ error: 'Credenciales inválidas' });
    }
  } catch (error) {
    console.error('Error en simple login:', error);
    res.status(500).json({ error: 'Error en el servidor' });
  }
});

// Rutas API
app.use('/api/auth', authRoutes);
app.use('/api', apiRoutes);

if (isDevelopment) {
  // En desarrollo, solo manejar rutas API
  app.get('/', (req, res) => {
    res.json({ message: 'API server running' });
  });
} else {
  // En producción, servir archivos estáticos
  app.use(express.static(path.join(__dirname, 'client/build')));
  
  // Todas las rutas no-api serán manejadas por React Router
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'client/build', 'index.html'));
  });
}

// Manejo de errores global
app.use((err, req, res, next) => {
  logger.error('Error no manejado:', { 
    error: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method
  });

  res.status(err.status || 500).json({
    error: isDevelopment ? err.message : 'Error interno del servidor'
  });
});

// Iniciar servidor
app.listen(port, () => {
  logger.info(`Servidor iniciado en el puerto ${port}`, {
    environment: process.env.NODE_ENV || 'desarrollo',
    port: port
  });
});
