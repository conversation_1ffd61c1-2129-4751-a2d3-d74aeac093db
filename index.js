const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
const logger = require('./server/utils/logger');

// Importar rutas
const authRoutes = require('./server/routes/auth');
const apiRoutes = require('./server/routes/api');

// Configuración de variables de entorno
dotenv.config();

const app = express();
const port = process.env.PORT || 3000;
const isDevelopment = process.env.NODE_ENV !== 'production';

// Configuración de CORS
const corsOptions = {
  origin: isDevelopment
    ? ['http://localhost:3001']  // Solo permitir el puerto de desarrollo de React
    : process.env.CORS_ORIGINS?.split(','),
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['Content-Range', 'X-Content-Range'],
  credentials: true,
  maxAge: 600
};

// Middleware de seguridad y configuración
app.use(helmet({
  crossOriginEmbedderPolicy: false, // Evitar problemas con CORS
  contentSecurityPolicy: false // Simplificar para desarrollo
}));
app.use(cors(corsOptions));
app.use(morgan('combined', { stream: logger.stream }));
app.use(express.json({ limit: '50kb' })); // Aumentar límite
app.use(express.urlencoded({ extended: true, limit: '50kb' })); // Aumentar límite

// Middleware para limpiar headers problemáticos
app.use((req, res, next) => {
  // Remover headers que pueden causar problemas de tamaño
  delete req.headers['x-forwarded-for'];
  delete req.headers['x-real-ip'];

  // Limitar el tamaño de cookies
  if (req.headers.cookie && req.headers.cookie.length > 4096) {
    req.headers.cookie = req.headers.cookie.substring(0, 4096);
  }

  next();
});

// Rutas API
app.use('/api/auth', authRoutes);
app.use('/api', apiRoutes);

if (isDevelopment) {
  // En desarrollo, solo manejar rutas API
  app.get('/', (req, res) => {
    res.json({ message: 'API server running' });
  });
} else {
  // En producción, servir archivos estáticos
  app.use(express.static(path.join(__dirname, 'client/build')));
  
  // Todas las rutas no-api serán manejadas por React Router
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'client/build', 'index.html'));
  });
}

// Manejo de errores global
app.use((err, req, res, next) => {
  logger.error('Error no manejado:', { 
    error: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method
  });

  res.status(err.status || 500).json({
    error: isDevelopment ? err.message : 'Error interno del servidor'
  });
});

// Iniciar servidor
app.listen(port, () => {
  logger.info(`Servidor iniciado en el puerto ${port}`, {
    environment: process.env.NODE_ENV || 'desarrollo',
    port: port
  });
});
