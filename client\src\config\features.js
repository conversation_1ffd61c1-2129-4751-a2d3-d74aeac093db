// Feature flags para controlar funcionalidades
export const FEATURES = {
  SHOW_SIMPLE_LOGIN: process.env.REACT_APP_SHOW_SIMPLE_LOGIN === 'true' || true, // Por defecto habilitado
  SHOW_ADVANCED_LOGIN: process.env.REACT_APP_SHOW_ADVANCED_LOGIN === 'true' || false, // Por defecto deshabilitado
  SHOW_DUMMY_DATA_WARNING: true, // Siempre mostrar warning para datos dummy
  DEBUG_MODE: process.env.NODE_ENV === 'development'
};

export default FEATURES;
