import React from 'react';
import { Link } from 'react-router-dom';
import FEATURES from '../config/features';

const WelcomePage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="max-w-4xl mx-auto px-6 py-12">
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            Portal Financiero
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Gestiona tu portafolio de inversiones con herramientas avanzadas de análisis y seguimiento en tiempo real.
          </p>
        </div>

        {/* Imagen financiera */}
        <div className="mb-12 flex justify-center">
          <div className="w-96 h-64 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-2xl flex items-center justify-center">
            {/* SVG de gráfico financiero */}
            <svg className="w-64 h-48 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M3 13h2v8H3v-8zm4-6h2v14H7V7zm4-4h2v18h-2V3zm4 9h2v9h-2v-9zm4-3h2v12h-2V9z"/>
              <circle cx="6" cy="4" r="2"/>
              <circle cx="14" cy="8" r="2"/>
              <circle cx="18" cy="12" r="2"/>
              <path d="M6 6l6 6 4-4 4 4" stroke="currentColor" strokeWidth="2" fill="none"/>
            </svg>
          </div>
        </div>

        {/* Características principales */}
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          <div className="text-center p-6 bg-white rounded-lg shadow-lg">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Dashboard Avanzado</h3>
            <p className="text-gray-600">Visualiza tu portafolio con gráficos interactivos y métricas en tiempo real.</p>
          </div>

          <div className="text-center p-6 bg-white rounded-lg shadow-lg">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Análisis Inteligente</h3>
            <p className="text-gray-600">Obtén insights y recomendaciones basadas en análisis de mercado.</p>
          </div>

          <div className="text-center p-6 bg-white rounded-lg shadow-lg">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"/>
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Asistente IA</h3>
            <p className="text-gray-600">Chatbot inteligente para consultas y análisis personalizados.</p>
          </div>
        </div>

        {/* Botones de acceso */}
        <div className="text-center space-y-4">
          <div className="space-x-4">
            {FEATURES.SHOW_SIMPLE_LOGIN && (
              <Link
                to="/simple-login"
                className="inline-block px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg shadow-lg hover:bg-blue-700 transition-colors"
              >
                Acceder al Portal
              </Link>
            )}
            {FEATURES.SHOW_ADVANCED_LOGIN && (
              <Link
                to="/login"
                className="inline-block px-8 py-3 bg-gray-600 text-white font-semibold rounded-lg shadow-lg hover:bg-gray-700 transition-colors"
              >
                Login Avanzado
              </Link>
            )}
          </div>

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg max-w-md mx-auto">
            <p className="text-sm text-blue-800 font-medium mb-2">Demo disponible:</p>
            <p className="text-xs text-blue-700">
              Usa <strong><EMAIL></strong> / <strong>user123</strong> para acceder a la demo con datos ficticios
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomePage;
