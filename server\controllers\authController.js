const authService = require('../services/authService');
const logger = require('../utils/logger');
const jwt = require('jsonwebtoken');
const User = require('../models/dummyUser'); // Ahora lee desde users.json

class AuthController {
  async login(req, res) {
    try {
      const { email, password } = req.body;

      const user = await User.findByEmail(email);
      
      if (!user) {
        logger.warn('Intento de login fallido, usuario no encontrado', { email });
        return res.status(401).json({ error: 'Credenciales inválidas' });
      }

      // La validación ahora es universal y segura para todos los usuarios.
      const passwordIsValid = await authService.comparePasswords(password, user.password);

      if (!passwordIsValid) {
        logger.warn('Intento de login fallido, contraseña incorrecta', { email, userId: user.id });
        return res.status(401).json({ error: 'Credenciales inválidas' });
      }

      // Crear un payload limpio para los tokens
      const userPayload = {
        id: user.id,
        email: user.email,
        role: user.role,
      };
      const token = await authService.generateToken(userPayload);
      const refreshToken = await authService.generateRefreshToken(userPayload);

      logger.info('Login exitoso', { userId: user.id });

      res.json({
        token,
        refreshToken,
        user: {
          id: user.id,
          email: user.email,
          role: user.role
        }
      });
    } catch (error) {
      logger.error('Error en login', { error: error.message });
      res.status(500).json({ error: 'Error en el servidor' });
    }
  }

  async refreshToken(req, res) {
    try {
      const { refreshToken: oldRefreshToken } = req.body;
      
      if (!oldRefreshToken) {
        return res.status(400).json({ error: 'Refresh token no proporcionado' });
      }

      const decoded = await authService.verifyRefreshToken(oldRefreshToken);
      const user = await User.findById(decoded.id);
      
      if (!user) {
        return res.status(401).json({ error: 'Usuario no encontrado' });
      }

      // Usar un payload limpio también al refrescar
      const userPayload = {
        id: user.id,
        email: user.email,
        role: user.role,
      };

      // Invalidar el refresh token que acabamos de usar
      await authService.invalidateUsedRefreshToken(oldRefreshToken);

      // Generar un par de tokens completamente nuevos
      const newAccessToken = await authService.generateToken(userPayload);
      const newRefreshToken = await authService.generateRefreshToken(userPayload);
      
      logger.info('Token refrescado', { userId: user.id });
      
      // Devolver ambos tokens al cliente
      res.json({ token: newAccessToken, refreshToken: newRefreshToken });
    } catch (error) {
      logger.error('Error al refrescar token', { error: error.message });
      res.status(401).json({ error: 'Refresh token inválido' });
    }
  }

  async logout(req, res) {
    try {
      const accessToken = req.headers.authorization?.split(' ')[1];
      const { refreshToken } = req.body;
      let userIdForLog = req.user?.id; // Intenta obtener el ID del middleware
      
      if (accessToken) {
        await authService.invalidateAccessToken(accessToken);
      }

      if (refreshToken) {
        // Invalidamos también el refresh token para un logout más seguro.
        await authService.invalidateUsedRefreshToken(refreshToken);
        
        // Si no teníamos el ID del usuario (ej. token de acceso expirado),
        // lo extraemos del refresh token para un log más completo.
        if (!userIdForLog) {
          const decoded = jwt.decode(refreshToken);
          userIdForLog = decoded?.id;
        }
      }

      logger.info('Logout exitoso', { userId: userIdForLog });
      res.json({ message: 'Logout exitoso' });
    } catch (error) {
      logger.error('Error en logout', { error: error.message });
      res.status(500).json({ error: 'Error en el servidor' });
    }
  }
}

module.exports = new AuthController();
