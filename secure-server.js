const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const validator = require('validator');
const crypto = require('crypto');

const app = express();
const port = 3001;

// Generar secretos seguros si no existen en variables de entorno
const JWT_SECRET = process.env.JWT_SECRET || crypto.randomBytes(64).toString('hex');
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || crypto.randomBytes(64).toString('hex');

console.log('🔐 Secretos JWT generados de forma segura');

// Configuración de seguridad con Helmet
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Rate limiting agresivo para login
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 5, // máximo 5 intentos por IP
  message: {
    error: 'Demasiados intentos de login. Intenta nuevamente en 15 minutos.',
    retryAfter: 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true
});

// Rate limiting general para API
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 100, // máximo 100 requests por IP
  message: { error: 'Demasiadas peticiones. Intenta nuevamente más tarde.' }
});

// CORS seguro
app.use((req, res, next) => {
  const allowedOrigins = ['http://localhost:3000', 'http://localhost:3001'];
  const origin = req.headers.origin;
  
  if (allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  }
  
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.header('Access-Control-Max-Age', '86400'); // 24 horas
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Parser JSON con límites estrictos
app.use(express.json({ 
  limit: '1kb',
  strict: true
}));

// Aplicar rate limiting
app.use('/api', apiLimiter);

// Middleware de validación y sanitización
const validateAndSanitizeLogin = (req, res, next) => {
  const { email, password } = req.body;
  
  // Validación de entrada
  if (!email || !password) {
    return res.status(400).json({ 
      error: 'Email y contraseña son requeridos',
      code: 'MISSING_CREDENTIALS'
    });
  }
  
  // Validar formato de email
  if (!validator.isEmail(email)) {
    return res.status(400).json({ 
      error: 'Formato de email inválido',
      code: 'INVALID_EMAIL'
    });
  }
  
  // Validar longitud de contraseña
  if (password.length < 6 || password.length > 128) {
    return res.status(400).json({ 
      error: 'Contraseña debe tener entre 6 y 128 caracteres',
      code: 'INVALID_PASSWORD_LENGTH'
    });
  }
  
  // Sanitizar email
  req.body.email = validator.normalizeEmail(email);
  
  next();
};

// Usuario dummy seguro (en producción esto vendría de una base de datos)
const DUMMY_USER = {
  id: "user-02",
  email: "<EMAIL>",
  // Hash de "user123" con bcrypt
  passwordHash: "$2a$12$7FJHAkk3.kxFtuoYAc.qoOpanOUpLOzGxoV3BGaDapKeOq09saScK",
  role: "user",
  isDummy: true,
  createdAt: new Date().toISOString(),
  lastLogin: null
};

// Blacklist de tokens (en producción usar Redis)
const tokenBlacklist = new Set();

// Función para generar tokens JWT seguros
const generateTokens = (user) => {
  const payload = {
    id: user.id,
    email: user.email,
    role: user.role,
    isDummy: user.isDummy,
    iat: Math.floor(Date.now() / 1000)
  };
  
  const accessToken = jwt.sign(payload, JWT_SECRET, { 
    expiresIn: '1h',
    issuer: 'pf-dashboard',
    audience: 'pf-dashboard-client'
  });
  
  const refreshToken = jwt.sign(
    { id: user.id, type: 'refresh' }, 
    JWT_REFRESH_SECRET, 
    { 
      expiresIn: '7d',
      issuer: 'pf-dashboard',
      audience: 'pf-dashboard-client'
    }
  );
  
  return { accessToken, refreshToken };
};

// Middleware de autenticación
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ 
      error: 'Token de acceso requerido',
      code: 'MISSING_TOKEN'
    });
  }
  
  // Verificar si el token está en la blacklist
  if (tokenBlacklist.has(token)) {
    return res.status(401).json({ 
      error: 'Token revocado',
      code: 'REVOKED_TOKEN'
    });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ 
        error: 'Token inválido o expirado',
        code: 'INVALID_TOKEN'
      });
    }
    
    req.user = user;
    next();
  });
};

// Endpoint de login seguro
app.post('/api/auth/login', loginLimiter, validateAndSanitizeLogin, async (req, res) => {
  try {
    const { email, password } = req.body;
    const clientIP = req.ip || req.connection.remoteAddress;
    
    console.log(`🔐 Intento de login desde IP: ${clientIP}, Email: ${email}`);
    
    // Verificar si el usuario existe
    if (email !== DUMMY_USER.email) {
      console.log(`❌ Usuario no encontrado: ${email}`);
      // Delay intencional para prevenir timing attacks
      await new Promise(resolve => setTimeout(resolve, 1000));
      return res.status(401).json({ 
        error: 'Credenciales inválidas',
        code: 'INVALID_CREDENTIALS'
      });
    }
    
    // Verificar contraseña
    const isValidPassword = await bcrypt.compare(password, DUMMY_USER.passwordHash);
    
    if (!isValidPassword) {
      console.log(`❌ Contraseña incorrecta para: ${email}`);
      // Delay intencional para prevenir timing attacks
      await new Promise(resolve => setTimeout(resolve, 1000));
      return res.status(401).json({ 
        error: 'Credenciales inválidas',
        code: 'INVALID_CREDENTIALS'
      });
    }
    
    // Generar tokens
    const { accessToken, refreshToken } = generateTokens(DUMMY_USER);
    
    // Actualizar último login
    DUMMY_USER.lastLogin = new Date().toISOString();
    
    console.log(`✅ Login exitoso para: ${email}`);
    
    res.json({
      success: true,
      token: accessToken,
      refreshToken: refreshToken,
      user: {
        id: DUMMY_USER.id,
        email: DUMMY_USER.email,
        role: DUMMY_USER.role,
        isDummy: DUMMY_USER.isDummy,
        lastLogin: DUMMY_USER.lastLogin
      },
      expiresIn: 3600 // 1 hora en segundos
    });
    
  } catch (error) {
    console.error('❌ Error en login:', error);
    res.status(500).json({ 
      error: 'Error interno del servidor',
      code: 'INTERNAL_ERROR'
    });
  }
});

// Endpoint de logout seguro
app.post('/api/auth/logout', authenticateToken, (req, res) => {
  const token = req.headers['authorization'].split(' ')[1];
  
  // Agregar token a blacklist
  tokenBlacklist.add(token);
  
  console.log(`🔐 Logout exitoso para usuario: ${req.user.email}`);
  
  res.json({ 
    success: true, 
    message: 'Logout exitoso' 
  });
});

// Endpoint de verificación de token
app.get('/api/auth/verify', authenticateToken, (req, res) => {
  res.json({
    valid: true,
    user: {
      id: req.user.id,
      email: req.user.email,
      role: req.user.role,
      isDummy: req.user.isDummy
    }
  });
});

// Endpoint de portfolio con autenticación
app.get('/api/portfolio', authenticateToken, (req, res) => {
  // Datos dummy para el dashboard
  res.json({
    data: {
      totalValue: 150000,
      dailyChange: 2.5,
      dailyChangePercent: 1.67,
      positions: [
        { symbol: 'AAPL', quantity: 100, currentPrice: 150, totalValue: 15000 },
        { symbol: 'GOOGL', quantity: 50, currentPrice: 2500, totalValue: 125000 },
        { symbol: 'MSFT', quantity: 30, currentPrice: 300, totalValue: 9000 }
      ],
      performance: {
        '1D': 1.67,
        '1W': 3.2,
        '1M': 8.5,
        '3M': 12.1,
        '1Y': 24.8
      }
    },
    metadata: {
      lastUpdated: new Date().toISOString(),
      source: 'secure-server',
      userId: req.user.id,
      isDummy: req.user.isDummy
    }
  });
});

// Endpoint de salud
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Manejo de errores global
app.use((err, req, res, next) => {
  console.error('❌ Error no manejado:', err);
  res.status(500).json({ 
    error: 'Error interno del servidor',
    code: 'INTERNAL_ERROR'
  });
});

// Manejo de rutas no encontradas
app.use('*', (req, res) => {
  res.status(404).json({ 
    error: 'Endpoint no encontrado',
    code: 'NOT_FOUND'
  });
});

// Iniciar servidor
app.listen(port, () => {
  console.log(`🚀 Servidor seguro iniciado en puerto ${port}`);
  console.log(`🔐 Medidas de seguridad activas:`);
  console.log(`   - Rate limiting habilitado`);
  console.log(`   - Headers de seguridad configurados`);
  console.log(`   - Validación de entrada activa`);
  console.log(`   - Tokens JWT seguros`);
  console.log(`   - Blacklist de tokens implementada`);
  console.log(`\n📧 Credenciales de demo: <EMAIL> / user123`);
});
