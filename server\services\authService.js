const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const logger = require('../utils/logger');

class AuthService {
  constructor() {
    this.tokenBlacklist = new Set();
    this.usedRefreshTokens = new Set();
  }

  async generateToken(user) {
    return jwt.sign(
      { 
        id: user.id, 
        email: user.email,
        role: user.role 
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );
  }

  async generateRefreshToken(user) {
    return jwt.sign(
      { id: user.id },
      process.env.JWT_REFRESH_SECRET,
      { expiresIn: '7d' }
    );
  }

  async verifyToken(token) {
    if (this.tokenBlacklist.has(token)) { // Para access tokens durante el logout
      throw new Error('Token revocado');
    }
    return jwt.verify(token, process.env.JWT_SECRET);
  }

  async verifyRefreshToken(token) {
    if (this.usedRefreshTokens.has(token)) {
      // Este token ya ha sido usado, lo que podría indicar un robo.
      logger.warn('Intento de reutilización de refresh token detectado.', { token });
      throw new Error('Refresh token reutilizado');
    }
    const decoded = jwt.verify(token, process.env.JWT_REFRESH_SECRET);
    return decoded;
  }

  async hashPassword(password) {
    return bcrypt.hash(password, 12);
  }

  async comparePasswords(password, hashedPassword) {
    return bcrypt.compare(password, hashedPassword);
  }

  async invalidateAccessToken(token) {
    this.tokenBlacklist.add(token);
  }

  async invalidateUsedRefreshToken(token) {
    this.usedRefreshTokens.add(token);
  }
}

module.exports = new AuthService();