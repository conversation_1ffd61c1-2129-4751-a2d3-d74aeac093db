import React, { useState, useEffect } from 'react';
import DummyDataBanner from './DummyDataBanner';

const DUMMY_DATA = [
  { id: 1, name: 'Portfolio Total', value: 124563.00, color: 'green', icon: 'M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3' },
  { id: 2, name: 'Inversiones en Brokers', value: 84286.00, color: 'blue', icon: 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6' },
  { id: 3, name: 'Inversiones en Bancos', value: 28947.00, color: 'purple', icon: 'M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3' },
  { id: 4, name: 'Efectivo y Wallets', value: 11330.00, color: 'yellow', icon: 'M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z' },
];

const DummyDataIndicator = () => (
  <div className="absolute top-2 right-2 flex items-center gap-2" title="Datos de ejemplo">
    <div className="relative">
      <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
      <div className="absolute top-0 left-0 w-2 h-2 bg-orange-400 rounded-full animate-ping"></div>
    </div>
    <svg 
      className="h-4 w-4 text-yellow-400" 
      fill="currentColor" 
      viewBox="0 0 20 20"
    >
      <path 
        fillRule="evenodd" 
        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" 
        clipRule="evenodd" 
      />
    </svg>
  </div>
);

const Dashboard = () => {
  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Banner de datos ficticios */}
      <DummyDataBanner />
      {/* Stats Cards - Ajustado para mejor visualización en móvil */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
        {/* Portfolio Total */}
        <div className="bg-white rounded-lg shadow p-4 sm:p-6 relative">
          <DummyDataIndicator />
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0"> {/* Previene desbordamiento de texto */}
              <p className="text-sm text-gray-500 truncate">Portfolio Total</p>
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">$124,563.00</h3>
            </div>
            <div className="bg-green-100 p-2 sm:p-3 rounded-full flex-shrink-0 ml-3">
              <svg className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"/>
              </svg>
            </div>
          </div>
          <div className="mt-3 sm:mt-4 flex items-center">
            <span className="text-red-500 text-sm font-medium">-0.8%</span>
            <span className="text-gray-500 text-sm ml-2 truncate">vs mes anterior</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4 sm:p-6 relative">
          <DummyDataIndicator />
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-500 truncate">Inversiones en Brokers</p>
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">$84,286.00</h3>
            </div>
            <div className="bg-blue-100 p-2 sm:p-3 rounded-full flex-shrink-0 ml-3">
              <svg className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
              </svg>
            </div>
          </div>
          <div className="mt-3 sm:mt-4 flex items-center">
            <span className="text-green-500 text-sm font-medium">+3.2%</span>
            <span className="text-gray-500 text-sm ml-2 truncate">vs mes anterior</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4 sm:p-6 relative">
          <DummyDataIndicator />
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-500 truncate">Inversiones en Bancos</p>
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">$28,947.00</h3>
            </div>
            <div className="bg-purple-100 p-2 sm:p-3 rounded-full flex-shrink-0 ml-3">
              <svg className="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"/>
              </svg>
            </div>
          </div>
          <div className="mt-3 sm:mt-4 flex items-center">
            <span className="text-red-500 text-sm font-medium">-0.8%</span>
            <span className="text-gray-500 text-sm ml-2 truncate">vs mes anterior</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4 sm:p-6 relative">
          <DummyDataIndicator />
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-500 truncate">Efectivo y Wallets</p>
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">$11,330.00</h3>
            </div>
            <div className="bg-yellow-100 p-2 sm:p-3 rounded-full flex-shrink-0 ml-3">
              <svg className="w-5 h-5 sm:w-6 sm:h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
              </svg>
            </div>
          </div>
          <div className="mt-3 sm:mt-4 flex items-center">
            <span className="text-green-500 text-sm font-medium">+1.4%</span>
            <span className="text-gray-500 text-sm ml-2 truncate">vs mes anterior</span>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {/* Rendimientos Card */}
        <div className="bg-white rounded-lg shadow p-4 sm:p-6">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">Rendimientos</h3>
          
          {/* Rendimientos Grid - Scrollable en móvil */}
          <div className="overflow-x-auto -mx-4 sm:mx-0 px-4 sm:px-0 pb-2 sm:pb-0">
            <div className="inline-grid grid-cols-5 gap-3 min-w-[600px] sm:min-w-0 sm:grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 mb-6">
              {/* Rendimiento Diario */}
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
                <p className="text-xs sm:text-sm text-gray-500 mb-1">Diario</p>
                <div className="flex items-center">
                  <span className="text-lg sm:text-xl font-bold text-green-600">+1.2%</span>
                  <span className="ml-2 text-xs text-gray-500">
                    <span className="text-red-500">-0.3%</span> prev
                  </span>
                </div>
              </div>

              {/* Rendimiento Semanal */}
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
                <p className="text-xs sm:text-sm text-gray-500 mb-1">Semanal</p>
                <div className="flex items-center">
                  <span className="text-lg sm:text-xl font-bold text-green-600">+3.8%</span>
                  <span className="ml-2 text-xs text-gray-500">
                    <span className="text-green-500">+2.1%</span> prev
                  </span>
                </div>
              </div>

              {/* Rendimiento Mensual */}
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
                <p className="text-xs sm:text-sm text-gray-500 mb-1">Mensual</p>
                <div className="flex items-center">
                  <span className="text-lg sm:text-xl font-bold text-red-600">-2.5%</span>
                  <span className="ml-2 text-xs text-gray-500">
                    <span className="text-green-500">+5.4%</span> prev
                  </span>
                </div>
              </div>

              {/* Rendimiento YTD */}
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
                <p className="text-xs sm:text-sm text-gray-500 mb-1">YTD</p>
                <div className="flex items-center">
                  <span className="text-lg sm:text-xl font-bold text-green-600">+12.4%</span>
                  <span className="ml-2 text-xs text-gray-500">
                    <span className="text-green-500">+8.7%</span> prev
                  </span>
                </div>
              </div>

              {/* Rendimiento Anual */}
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
                <p className="text-xs sm:text-sm text-gray-500 mb-1">Anual</p>
                <div className="flex items-center">
                  <span className="text-lg sm:text-xl font-bold text-green-600">+15.7%</span>
                  <span className="ml-2 text-xs text-gray-500">
                    <span className="text-green-500">+11.2%</span> prev
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Gráfico */}
          <div className="h-48 sm:h-64 bg-gray-50 rounded flex items-center justify-center">
            <p className="text-sm sm:text-base text-gray-500">Gráfico de rendimientos históricos</p>
          </div>
        </div>

        {/* Distribución de Activos Card */}
        <div className="bg-white rounded-lg shadow p-4 sm:p-6">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">Distribución de Activos</h3>
          <div className="h-48 sm:h-80 bg-gray-50 rounded flex items-center justify-center">
            <p className="text-sm sm:text-base text-gray-500">Gráfico de distribución</p>
          </div>
        </div>
      </div>

      {/* Recent Transactions */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-4 sm:p-6">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">Transacciones Recientes</h3>
          
          {/* Tabla responsive con scroll horizontal en móvil */}
          <div className="overflow-x-auto -mx-4 sm:mx-0">
            <div className="inline-block min-w-full align-middle">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="text-left text-xs sm:text-sm font-medium text-gray-500">
                    <th scope="col" className="py-3 px-4 sm:px-6">Fecha</th>
                    <th scope="col" className="py-3 px-4 sm:px-6">Tipo</th>
                    <th scope="col" className="py-3 px-4 sm:px-6">Descripción</th>
                    <th scope="col" className="py-3 px-4 sm:px-6">Monto</th>
                    <th scope="col" className="py-3 px-4 sm:px-6">Estado</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  <tr className="text-xs sm:text-sm text-gray-900">
                    <td className="whitespace-nowrap py-3 px-4 sm:px-6">2024-02-15</td>
                    <td className="whitespace-nowrap py-3 px-4 sm:px-6">Compra</td>
                    <td className="whitespace-nowrap py-3 px-4 sm:px-6">AAPL Stock</td>
                    <td className="whitespace-nowrap py-3 px-4 sm:px-6">$2,450.00</td>
                    <td className="whitespace-nowrap py-3 px-4 sm:px-6">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Completado
                      </span>
                    </td>
                  </tr>
                  {/* Más filas... */}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
