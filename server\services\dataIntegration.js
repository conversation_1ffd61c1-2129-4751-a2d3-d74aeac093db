const { google } = require('googleapis');
const axios = require('axios');
const NodeCache = require('node-cache');
const logger = require('../utils/logger');
const { encrypt, decrypt } = require('../utils/encryption');

class DataIntegrationService {
  constructor() {
    this.sheets = google.sheets({ version: 'v4' });
    // Usamos node-cache para un manejo automático de la expiración (TTL en segundos)
    this.cache = new NodeCache({ stdTTL: 1800 }); // 30 minutos
    this.ppiApiToken = null;
    this.ppiTokenExpiry = 0;
  }

  // Método para obtener y gestionar el token de la API de PPI
  async getPpiApiToken() {
    // Si el token existe y no ha expirado, lo reutilizamos.
    if (this.ppiApiToken && this.ppiTokenExpiry > Date.now()) {
      return this.ppiApiToken;
    }

    // Lógica para obtener un nuevo token de PPI.
    // Esto es un EJEMPLO y debe ser adaptado a la autenticación de PPI (ej. OAuth2).
    logger.info('Obteniendo nuevo token de la API de PPI...');
    // const response = await axios.post(`${process.env.FINANCIAL_PORTAL_API}/auth/token`, {
    //   client_id: process.env.PPI_API_KEY,
    //   client_secret: process.env.PPI_API_SECRET,
    // });
    // this.ppiApiToken = response.data.access_token;
    // this.ppiTokenExpiry = Date.now() + (response.data.expires_in * 1000);
    this.ppiApiToken = process.env.PPI_API_KEY; // Usando la API Key directamente si es el caso
    return this.ppiApiToken;
  }

  async getAuthenticatedClient() {
    const auth = new google.auth.GoogleAuth({
      keyFile: process.env.GOOGLE_SERVICE_ACCOUNT_KEY,
      scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
    });
    return auth.getClient();
  }

  // Método para validar y transformar datos de PPI
  // DEBES ADAPTAR ESTO a la respuesta real de la API de PPI
  transformPpiData(ppiData) {
    return {
      timestamp: new Date().toISOString(),
      portfolioValue: Number(ppiData.totalAmount || 0),
      assets: Array.isArray(ppiData.accountStatus?.assets) ? ppiData.accountStatus.assets.map(asset => ({
        ticker: asset.symbol,
        description: asset.description,
        quantity: Number(asset.quantity || 0),
        value: Number(asset.marketValue || 0),
      })) : [],
      performance: {
        daily: Number(ppiData.performance?.dayVariation || 0),
        // Asume que la API puede no devolver estos datos directamente
        weekly: 0, 
        monthly: 0
      }
    };
  }

  // Método mejorado para obtener datos del portal financiero
  async fetchFinancialPortalData(userId, timeRange) {
    // Usamos el timeRange en la clave de caché para almacenar diferentes periodos
    const cacheKey = `ppi_portfolio_${userId}_${timeRange || 'default'}`;
    const cachedData = this.cache.get(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    try {
      const apiToken = await this.getPpiApiToken();
      const response = await axios.get(`${process.env.FINANCIAL_PORTAL_API}/account/portfolio`, {
        headers: {
          'Authorization': `Bearer ${apiToken}`,
        },
        params: { timeRange }
      });

      const transformedData = this.transformPpiData(response.data);
      // El caché guardará los datos por el tiempo definido en el constructor (stdTTL)
      this.cache.set(cacheKey, transformedData);

      return transformedData;
    } catch (error) {
      logger.error('Error fetching financial portal data from PPI', { 
        error: error.response?.data || error.message,
        userId 
      });
      throw new Error('Error al obtener datos del portal financiero');
    }
  }

  // Método mejorado para obtener datos de Google Sheets
  async fetchGoogleSheetsData(spreadsheetId, range) {
    try {
      const auth = await this.getAuthenticatedClient();
      const response = await this.sheets.spreadsheets.values.get({
        auth,
        spreadsheetId,
        range,
      });

      return this.transformSheetsData(response.data.values);
    } catch (error) {
      console.error('Error fetching Google Sheets data:', error);
      throw new Error('Error al obtener datos de Google Sheets');
    }
  }

  // Método para transformar datos de Google Sheets
  transformSheetsData(values) {
    if (!values || !Array.isArray(values)) return [];

    return values.map(row => ({
      date: row[0],
      type: row[1],
      description: row[2],
      amount: Number(row[3] || 0),
      status: row[4]
    }));
  }
}

module.exports = new DataIntegrationService();
