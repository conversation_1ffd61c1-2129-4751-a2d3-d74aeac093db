const express = require('express');
const router = express.Router();
const dataIntegrationService = require('../services/dataIntegration');
const { authenticateToken, apiLimiter, checkRole } = require('../middleware/auth');
const logger = require('../utils/logger');

// Aplicar rate limiting a todas las rutas de la API
router.use(apiLimiter);

// Aplicar autenticación a todas las rutas
router.use(authenticateToken);

// Rutas del dashboard
router.get('/portfolio', 
  checkRole(['user', 'admin']),
  async (req, res) => {
    try {
      const { timeRange } = req.query; // Capturamos el timeRange desde la query
      logger.info('Solicitando datos de portfolio', { userId: req.user.id, timeRange });
      
      // Llamamos al servicio de integración para obtener los datos de PPI
      const portfolioData = await dataIntegrationService.fetchFinancialPortalData(req.user.id, timeRange);
      res.json(portfolioData);
    } catch (error) {
      logger.error('Error al obtener portfolio', {
        error: error.message,
        userId: req.user.id
      });
      res.status(500).json({ error: 'Error al obtener datos' });
    }
  }
);

router.get('/historical-data',
  checkRole(['user', 'admin']),
  async (req, res) => {
    try {
      logger.info('Acceso a datos históricos', { 
        userId: req.user.id,
        params: req.query
      });
      
      // Implementación del endpoint
      res.json({ data: 'Datos históricos' });
    } catch (error) {
      logger.error('Error al obtener datos históricos', {
        error: error.message,
        userId: req.user.id
      });
      res.status(500).json({ error: 'Error al obtener datos' });
    }
  }
);

// Rutas administrativas
router.post('/admin/users',
  checkRole(['admin']),
  async (req, res) => {
    try {
      logger.info('Creación de nuevo usuario', {
        adminId: req.user.id,
        newUserData: req.body
      });
      
      // Implementación del endpoint
      res.status(201).json({ message: 'Usuario creado' });
    } catch (error) {
      logger.error('Error al crear usuario', {
        error: error.message,
        adminId: req.user.id
      });
      res.status(500).json({ error: 'Error al crear usuario' });
    }
  }
);

module.exports = router;
