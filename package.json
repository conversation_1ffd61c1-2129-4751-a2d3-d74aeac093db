{"name": "pfdashboard", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node --max-http-header-size=32768 index.js", "dev": "nodemon --exec \"node --max-http-header-size=32768 index.js\"", "client": "cd client && npm start", "dev:full": "concurrently \"npm run dev\" \"npm run client\"", "build": "cd client && npm run build", "test": "jest", "lint": "eslint ."}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "googleapis": "^129.0.0", "helmet": "^7.1.0", "node-cache": "^5.1.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "winston": "^3.17.0"}, "devDependencies": {"autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}}