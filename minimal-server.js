const express = require('express');
const app = express();
const port = 3001; // Puerto diferente para evitar conflictos

// Configuración mínima de CORS
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Parser JSON mínimo
app.use(express.json({ limit: '1kb' }));

// Usuarios hardcodeados para evitar dependencias
const users = [
  {
    id: "sandbox-user-01",
    email: "<EMAIL>",
    password: "admin123", // Contraseña en texto plano para simplificar
    role: "admin"
  },
  {
    id: "user-02",
    email: "<EMAIL>",
    password: "user123", // Contraseña en texto plano para simplificar
    role: "user"
  }
];

// Endpoint de login ultra-simple
app.post('/api/auth/login', (req, res) => {
  try {
    console.log('Login request received:', req.body);
    
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({ error: 'Email y contraseña requeridos' });
    }
    
    // Buscar usuario
    const user = users.find(u => u.email.toLowerCase() === email.toLowerCase());
    
    if (!user) {
      console.log('Usuario no encontrado:', email);
      return res.status(401).json({ error: 'Credenciales inválidas' });
    }
    
    // Verificar contraseña (sin hash para simplificar)
    if (user.password !== password) {
      console.log('Contraseña incorrecta para:', email);
      return res.status(401).json({ error: 'Credenciales inválidas' });
    }
    
    // Login exitoso
    console.log('Login exitoso para:', email);
    
    const token = `token-${user.id}-${Date.now()}`;
    
    res.json({
      success: true,
      token: token,
      refreshToken: `refresh-${user.id}-${Date.now()}`,
      user: {
        id: user.id,
        email: user.email,
        role: user.role
      }
    });
    
  } catch (error) {
    console.error('Error en login:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// Endpoint de prueba
app.get('/api/test', (req, res) => {
  res.json({ message: 'Servidor mínimo funcionando', timestamp: new Date().toISOString() });
});

// Endpoint para obtener datos del portfolio (dummy)
app.get('/api/portfolio', (req, res) => {
  // Datos dummy para el dashboard
  res.json({
    data: {
      totalValue: 150000,
      dailyChange: 2.5,
      dailyChangePercent: 1.67,
      positions: [
        { symbol: 'AAPL', quantity: 100, currentPrice: 150, totalValue: 15000 },
        { symbol: 'GOOGL', quantity: 50, currentPrice: 2500, totalValue: 125000 },
        { symbol: 'MSFT', quantity: 30, currentPrice: 300, totalValue: 9000 }
      ],
      performance: {
        '1D': 1.67,
        '1W': 3.2,
        '1M': 8.5,
        '3M': 12.1,
        '1Y': 24.8
      }
    },
    metadata: {
      lastUpdated: new Date().toISOString(),
      source: 'minimal-server'
    }
  });
});

// Manejo de errores global
app.use((err, req, res, next) => {
  console.error('Error no manejado:', err);
  res.status(500).json({ error: 'Error interno del servidor' });
});

// Iniciar servidor
app.listen(port, () => {
  console.log(`Servidor mínimo iniciado en puerto ${port}`);
  console.log(`Prueba: http://localhost:${port}/api/test`);
  console.log('Credenciales de prueba:');
  console.log('- Admin: <EMAIL> / admin123');
  console.log('- Usuario: <EMAIL> / user123');
});
