import axios from 'axios';

// Configuración del servidor seguro
const baseURL = 'http://localhost:3001/api';
const api = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: false,
  timeout: 10000,
  // Configuración adicional de seguridad
  maxContentLength: 10000,
  maxBodyLength: 10000,
});

// Interceptor para añadir el token de autenticación a cada petición.
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
}, (error) => Promise.reject(error));

// Interceptor para manejar errores de forma segura
api.interceptors.response.use(
  (response) => response,
  async (error) => {

    // Manejar diferentes tipos de errores
    if (error.response?.status === 401) {
      // Token inválido o expirado
      console.warn('Token inválido o expirado, redirigiendo al login');
      localStorage.clear();
      window.location.href = '/';
      return Promise.reject(error);
    }

    if (error.response?.status === 429) {
      // Rate limit excedido
      console.warn('Rate limit excedido');
      return Promise.reject(new Error('Demasiadas peticiones. Intenta nuevamente más tarde.'));
    }

    if (error.response?.status === 403) {
      // Acceso denegado
      console.warn('Acceso denegado');
      return Promise.reject(new Error('No tienes permisos para realizar esta acción.'));
    }

    return Promise.reject(error);
  }
);

export const fetchPortfolioData = async (timeRange) => {
  try {
    const { data } = await api.get('/portfolio', {
      params: { timeRange }
    });
    return data;
  } catch (error) {
    console.error('Error fetching portfolio data:', error);
    throw error;
  }
};

// Función de login segura
export const login = async (credentials) => {
  try {
    // Validar credenciales en el cliente
    if (!credentials.email || !credentials.password) {
      throw new Error('Email y contraseña son requeridos');
    }

    const { data } = await api.post('/auth/login', credentials);

    if (data.success && data.token) {
      localStorage.setItem('token', data.token);
      if (data.refreshToken) {
        localStorage.setItem('refreshToken', data.refreshToken);
      }
      console.log('✅ Login exitoso');
      return data;
    } else {
      throw new Error('Respuesta de login inválida');
    }
  } catch (error) {
    console.error('❌ Error en login:', error);

    // Manejar errores específicos del servidor
    if (error.response?.data?.code) {
      switch (error.response.data.code) {
        case 'INVALID_CREDENTIALS':
          throw new Error('Email o contraseña incorrectos');
        case 'MISSING_CREDENTIALS':
          throw new Error('Email y contraseña son requeridos');
        case 'INVALID_EMAIL':
          throw new Error('Formato de email inválido');
        case 'INVALID_PASSWORD_LENGTH':
          throw new Error('La contraseña debe tener entre 6 y 128 caracteres');
        default:
          throw new Error(error.response.data.error || 'Error en el login');
      }
    }

    throw error;
  }
};

// Función de logout segura
export const logout = async () => {
  try {
    // Intentar logout en el servidor para invalidar el token
    await api.post('/auth/logout');
    console.log('✅ Logout exitoso en servidor');
  } catch (error) {
    console.warn('⚠️ Error en logout del servidor, limpiando localmente:', error.message);
  }

  // Limpiar almacenamiento local siempre
  localStorage.clear();
  console.log('🧹 Almacenamiento local limpiado');
};

// Función para verificar si el token es válido
export const verifyToken = async () => {
  try {
    const { data } = await api.get('/auth/verify');
    return data;
  } catch (error) {
    console.warn('Token inválido:', error.message);
    localStorage.clear();
    return null;
  }
};

export default api;
