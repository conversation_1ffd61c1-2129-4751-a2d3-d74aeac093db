import axios from 'axios';

// Workaround: Apuntamos directamente al backend para evitar problemas con el proxy de CRA.
// Tu configuración de CORS en el servidor ya permite esta comunicación directa.
const baseURL = 'http://localhost:3000/api'; 
const api = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: false, // Cambiar a false para evitar cookies problemáticas
  timeout: 10000, // Timeout de 10 segundos
});

// Interceptor para añadir el token de autenticación a cada petición.
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
}, (error) => Promise.reject(error));

// Interceptor para manejar errores, especialmente el 401 (No autorizado).
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Si el error es 401 y no es una petición de reintento.
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true; // Marcar como reintento para evitar bucles infinitos.

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (!refreshToken) {
          // Si no hay refresh token, desloguear.
          localStorage.clear();
          window.location.href = '/login';
          return Promise.reject(error);
        }

        // Pedir un nuevo token de acceso usando el refresh token.
        const { data } = await api.post('/auth/refresh-token', { refreshToken });
        
        // Guardar el nuevo token de acceso.
        localStorage.setItem('token', data.token);
        // Si el servidor devuelve un nuevo refresh token (rotación), guardarlo también.
        if (data.refreshToken) {
          localStorage.setItem('refreshToken', data.refreshToken);
        }
        
        // Actualizar el header de la petición original y reintentarla.
        // Es importante actualizar ambos, el default para futuras peticiones y el de la petición actual.
        api.defaults.headers.common['Authorization'] = `Bearer ${data.token}`;
        originalRequest.headers['Authorization'] = `Bearer ${data.token}`;
        
        return api(originalRequest);
      } catch (refreshError) {
        // Si el refresh token también falla, desloguear al usuario.
        console.error('No se pudo refrescar el token:', refreshError);
        localStorage.clear();
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export const fetchPortfolioData = async (timeRange) => {
  try {
    const { data } = await api.get('/portfolio', {
      params: { timeRange }
    });
    return data;
  } catch (error) {
    console.error('Error fetching portfolio data:', error);
    throw error;
  }
};

// Necesitamos una forma de guardar el refresh token en el cliente.
export const login = async (credentials) => {
  const { data } = await api.post('/auth/login', credentials);
  if (data.token && data.refreshToken) {
    localStorage.setItem('token', data.token);
    localStorage.setItem('refreshToken', data.refreshToken);
  }
  return data;
};

export const logout = async () => {
  try {
    const refreshToken = localStorage.getItem('refreshToken');
    // Enviamos el refresh token al servidor para que también lo invalide.
    await api.post('/auth/logout', { refreshToken });
  } catch (error) {
    console.error('Fallo el logout en el servidor, limpiando localmente de todas formas.', error);
  }
  // Limpiamos el almacenamiento local independientemente del resultado del servidor.
  localStorage.clear();
};

export default api;
