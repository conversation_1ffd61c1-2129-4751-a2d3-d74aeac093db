# Usar una imagen oficial de Node.js optimizada para producción
FROM node:18-alpine

WORKDIR /usr/src/app

# Copiar package.json y package-lock.json (o yarn.lock)
COPY package*.json ./

# Instalar solo dependencias de producción y limpiar caché
RUN npm ci --only=production && npm cache clean --force

# Copiar el resto de los archivos de la aplicación del backend
# Asegúrate de que solo copias lo necesario para el servidor
COPY ./server ./server
COPY index.js .
# Si tienes otros archivos/carpetas en la raíz que el backend necesita, cópialos también
# Ejemplo: COPY ./config ./config

# Exponer el puerto en el que corre la aplicación backend
# Este es el puerto DENTRO del contenedor
EXPOSE 3000

# Variable de entorno para asegurar que se ejecuta en modo producción
ENV NODE_ENV=production

# Comando para correr la aplicación
CMD [ "node", "index.js" ]
