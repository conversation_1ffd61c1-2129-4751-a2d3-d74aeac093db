const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');

const USERS_DB_PATH = path.join(__dirname, '../data/users.json');

let usersCache = [];

/**
 * Carga los usuarios desde el archivo JSON a una caché en memoria.
 * Esto evita leer el archivo en cada petición.
 */
async function loadUsers() {
  if (usersCache.length > 0) {
    return;
  }
  try {
    const data = await fs.readFile(USERS_DB_PATH, 'utf-8');
    usersCache = JSON.parse(data);
    logger.info(`Cargados ${usersCache.length} usuarios desde el archivo.`);
  } catch (error) {
    logger.error('No se pudo cargar la base de datos de usuarios desde el archivo', { error: error.message });
    // En caso de error, la caché permanecerá vacía.
  }
}

/**
 * Simulación de búsqueda en base de datos por email.
 * @param {string} email - El email del usuario a buscar.
 * @returns {Promise<object|null>} El objeto de usuario o null si no se encuentra.
 */
async function findByEmail(email) {
  await loadUsers(); // Asegura que la caché esté cargada
  const lowercasedEmail = email ? email.toLowerCase() : '';
  return usersCache.find(user => user.email.toLowerCase() === lowercasedEmail) || null;
}

async function findById(id) {
  await loadUsers(); // Asegura que la caché esté cargada
  return usersCache.find(user => user.id === id) || null;
}

// Carga inicial al iniciar el servidor
loadUsers();

module.exports = { findByEmail, findById };